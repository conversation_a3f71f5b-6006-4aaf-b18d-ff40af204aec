import {
  AdminNotificationsDocument,
  useAdminDeleteClubEventMutation,
  useAdminRemoveClubPostByIdMutation,
  useAdminUnflagReportsByEventIdMutation,
  useAdminUnflagReportsByPostIdMutation,
  useMarkAdminNotificationsAsReadMutation,
  useRejectedClubRequestCreationMutation,
  ClubRequest,
} from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';
import { TOAST_DURATION } from '@/lib/constants';
import { ApolloError, gql } from '@apollo/client';
import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const useNotificationActions = ({ refetch }: { refetch?: () => void }) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);

  // Track loading states per item ID
  const [loadingItems, setLoadingItems] = useState<{
    unflagging: Set<string>;
    removing: Set<string>;
    declining: Set<string>;
    markingAsRead: Set<string>;
  }>({
    unflagging: new Set(),
    removing: new Set(),
    declining: new Set(),
    markingAsRead: new Set(),
  });

  const [unflagPostMutation] = useAdminUnflagReportsByPostIdMutation();
  const [unflagEventMutation] = useAdminUnflagReportsByEventIdMutation();
  const [removePostMutation] = useAdminRemoveClubPostByIdMutation();
  const [removeEventMutation] = useAdminDeleteClubEventMutation();
  const [markAdminNotificationsAsRead, { loading: isMarkingAsRead }] =
    useMarkAdminNotificationsAsReadMutation();
  const [declineClubRequestCreation] = useRejectedClubRequestCreationMutation();

  const handleApiError = useCallback(
    (error: unknown, errorMessage = 'Operation failed') => {
      if (error instanceof ApolloError) {
        toast({
          variant: 'destructive',
          title: error.graphQLErrors[0]?.message || errorMessage,
          duration: TOAST_DURATION,
        });
      }
    },
    [toast]
  );

  const handleUnflagNotification = useCallback(
    async (isPostNotification: boolean, notificationId: string, itemId: string) => {
      // Add this item to unflagging loading state
      setLoadingItems((prev) => ({
        ...prev,
        unflagging: new Set([...Array.from(prev.unflagging), itemId]),
      }));

      try {
        if (isPostNotification) {
          await unflagPostMutation({
            variables: {
              postId: notificationId,
            },
            update: (cache) => {
              // Update the cache to set action to "UNFLAG"
              cache.modify({
                fields: {
                  adminNotifications(existing) {
                    if (!existing?.items) return existing;

                    // Find and update the notification with action = "UNFLAG"
                    existing.items.forEach((itemRef: any) => {
                      const notification = cache.readFragment<{
                        id: string;
                        payload?: { postId?: string };
                      }>({
                        id: itemRef.__ref,
                        fragment: gql`
                          fragment NotificationItemPost on AdminNotification {
                            id
                            payload {
                              postId
                            }
                          }
                        `,
                      });

                      if (notification?.payload?.postId === notificationId) {
                        cache.writeFragment({
                          id: itemRef.__ref,
                          fragment: gql`
                            fragment UpdateNotificationAction on AdminNotification {
                              action
                            }
                          `,
                          data: {
                            action: 'UNFLAG',
                          },
                        });
                      }
                    });

                    return existing;
                  },
                },
              });
            },
          });
        } else {
          await unflagEventMutation({
            variables: {
              eventId: notificationId,
            },
            update: (cache) => {
              // Update the cache to set action to "UNFLAG"
              cache.modify({
                fields: {
                  adminNotifications(existing) {
                    if (!existing?.items) return existing;

                    // Find and update the notification with action = "UNFLAG"
                    existing.items.forEach((itemRef: any) => {
                      const notification = cache.readFragment<{
                        id: string;
                        payload?: { eventId?: string };
                      }>({
                        id: itemRef.__ref,
                        fragment: gql`
                          fragment NotificationItemEvent on AdminNotification {
                            id
                            payload {
                              eventId
                            }
                          }
                        `,
                      });

                      if (notification?.payload?.eventId === notificationId) {
                        cache.writeFragment({
                          id: itemRef.__ref,
                          fragment: gql`
                            fragment UpdateNotificationActionEvent on AdminNotification {
                              action
                            }
                          `,
                          data: {
                            action: 'UNFLAG',
                          },
                        });
                      }
                    });

                    return existing;
                  },
                },
              });
            },
          });
        }
        toast({
          variant: 'success',
          title: `${isPostNotification ? 'Post' : 'Event'} unflagged successfully`,
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Failed to unflag notification');
      } finally {
        // Remove from unflagging loading state
        setLoadingItems((prev) => ({
          ...prev,
          unflagging: new Set(Array.from(prev.unflagging).filter((id) => id !== itemId)),
        }));
      }
    },
    [unflagPostMutation, unflagEventMutation, handleApiError, toast, setLoadingItems]
  );

  const handleRemoveNotification = useCallback(
    async (isPostNotification: boolean, notificationId: string, itemId: string) => {
      // Add this item to removing loading state
      setLoadingItems((prev) => ({
        ...prev,
        removing: new Set([...Array.from(prev.removing), itemId]),
      }));

      try {
        if (isPostNotification) {
          await removePostMutation({
            variables: {
              postId: notificationId,
            },
            update: (cache) => {
              // Remove the notification from the cache
              cache.modify({
                fields: {
                  adminNotifications(existing) {
                    if (!existing?.items) return existing;

                    // Filter out the notification that corresponds to the removed post
                    const filteredItems = existing.items.filter((itemRef: any) => {
                      const notification = cache.readFragment<{
                        id: string;
                        payload?: { postId?: string };
                      }>({
                        id: itemRef.__ref,
                        fragment: gql`
                          fragment NotificationItemPostRemove on AdminNotification {
                            id
                            payload {
                              postId
                            }
                          }
                        `,
                      });

                      // Keep notifications that don't match the removed post
                      return notification?.payload?.postId !== notificationId;
                    });

                    return {
                      ...existing,
                      items: filteredItems,
                      total: Math.max(
                        0,
                        existing.total - (existing.items.length - filteredItems.length)
                      ),
                    };
                  },
                },
              });
            },
          });
        } else {
          await removeEventMutation({
            variables: {
              clubEventId: notificationId,
            },
            update: (cache) => {
              // Remove the notification from the cache
              cache.modify({
                fields: {
                  adminNotifications(existing) {
                    if (!existing?.items) return existing;

                    // Filter out the notification that corresponds to the removed event
                    const filteredItems = existing.items.filter((itemRef: any) => {
                      const notification = cache.readFragment<{
                        id: string;
                        payload?: { eventId?: string };
                      }>({
                        id: itemRef.__ref,
                        fragment: gql`
                          fragment NotificationItemEventRemove on AdminNotification {
                            id
                            payload {
                              eventId
                            }
                          }
                        `,
                      });

                      // Keep notifications that don't match the removed event
                      return notification?.payload?.eventId !== notificationId;
                    });

                    return {
                      ...existing,
                      items: filteredItems,
                      total: Math.max(
                        0,
                        existing.total - (existing.items.length - filteredItems.length)
                      ),
                    };
                  },
                },
              });
            },
          });
        }

        toast({
          variant: 'success',
          title: `${isPostNotification ? 'Post' : 'Event'} removed successfully`,
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Failed to remove notification');
      } finally {
        // Remove from removing loading state
        setLoadingItems((prev) => ({
          ...prev,
          removing: new Set(Array.from(prev.removing).filter((id) => id !== itemId)),
        }));
      }
    },
    [toast, removePostMutation, removeEventMutation, handleApiError, setLoadingItems]
  );

  const handleMarkAsRead = useCallback(
    async ({
      isMarkAll = false,
      notificationId,
    }: {
      isMarkAll?: boolean;
      notificationId?: string;
    }) => {
      try {
        await markAdminNotificationsAsRead({
          variables: {
            input: {
              all: isMarkAll,
              notificationIds: notificationId ? [notificationId] : undefined,
            },
          },
          // optimisticResponse: {
          //   markAdminNotificationsAsRead: true,
          // },
          // update: (cache) => {
          //   // Optimistically update the cache
          //   cache.modify({
          //     fields: {
          //       adminNotifications(existing) {
          //         if (!existing?.items) return existing;

          //         const updatedItems = existing.items.map((itemRef: Reference) => {
          //           const item = cache.readFragment({
          //             id: itemRef.__ref,
          //             fragment: gql`
          //               fragment NotificationRead on AdminNotification {
          //                 id
          //                 isRead
          //               }
          //             `,
          //           }) as { id: string; isRead: boolean } | null;

          //           if (!item) return itemRef;

          //           if (isMarkAll || item.id === notificationId) {
          //             return cache.writeFragment({
          //               id: itemRef.__ref,
          //               fragment: gql`
          //                 fragment UpdateNotificationRead on AdminNotification {
          //                   isRead
          //                 }
          //               `,
          //               data: {
          //                 isRead: true,
          //               },
          //             });
          //           }

          //           return itemRef;
          //         });

          //         return {
          //           ...existing,
          //           items: updatedItems,
          //         };
          //       },
          //     },
          //   });
          // },
        });

        // Still refetch to ensure server state is in sync
        refetch?.();
      } catch (error) {
        handleApiError(error, 'Failed to mark admin notifications as read');
      }
    },
    [markAdminNotificationsAsRead, refetch, handleApiError]
  );

  const handleNavigate = useCallback(
    ({
      isPostReport,
      associationId,
      clubId,
      isClubCreationRequest,
    }: {
      isPostReport: boolean;
      associationId?: string;
      clubId?: string;
      isClubCreationRequest?: boolean;
    }) => {
      if (isClubCreationRequest) {
        navigate('/clubs?tab=requests');
        return;
      }

      const navigatePath = `/associations/${associationId}/clubs/${clubId}`;
      if (isPostReport) {
        navigate(`${navigatePath}?tab=posts`);
      } else {
        navigate(`${navigatePath}?tab=events`);
      }
    },
    [navigate]
  );

  const handleApproveClubRequest = useCallback(
    (clubRequest: ClubRequest) => {
      // Extract the required fields from the club request
      const clubRequestData = {
        category: clubRequest.category,
        clubAbout: clubRequest.clubAbout,
        clubDescription: clubRequest.clubDescription,
        clubName: clubRequest.clubName,
        requestId: clubRequest.id,
      };

      // Navigate to club creation page with pre-filled data
      navigate('/clubs/create', {
        state: {
          fromClubRequest: true,
          clubRequestData,
        },
      });

      setIsOpen(false);
    },
    [navigate]
  );

  const handleDeclineClubRequest = useCallback(
    async (requestId: string, itemId: string) => {
      // Add this item to declining loading state
      setLoadingItems((prev) => ({
        ...prev,
        declining: new Set([...Array.from(prev.declining), itemId]),
      }));

      try {
        await declineClubRequestCreation({
          variables: {
            clubRequestId: requestId,
          },
          refetchQueries: [AdminNotificationsDocument],
        });

        toast({
          variant: 'success',
          title: 'Club request declined successfully',
          duration: TOAST_DURATION,
        });

        // refetch?.();
      } catch (error) {
        handleApiError(error, 'Failed to decline club request');
      } finally {
        // Remove from declining loading state
        setLoadingItems((prev) => ({
          ...prev,
          declining: new Set(Array.from(prev.declining).filter((id) => id !== itemId)),
        }));
      }
    },
    [declineClubRequestCreation, handleApiError, refetch, toast, setLoadingItems]
  );

  // Helper function to get loading state for a specific item
  const getItemLoadingState = useCallback(
    (itemId: string) => ({
      isUnflagging: loadingItems.unflagging.has(itemId),
      isRemoving: loadingItems.removing.has(itemId),
      isDecliningClubRequest: loadingItems.declining.has(itemId),
      isMarkingAsRead: loadingItems.markingAsRead.has(itemId),
    }),
    [loadingItems]
  );

  return {
    isOpen,
    isMarkingAsRead,

    setIsOpen,
    handleUnflagNotification,
    handleRemoveNotification,
    handleNavigate,
    handleApproveClubRequest,
    handleDeclineClubRequest,
    handleMarkAsRead,
    getItemLoadingState,
  };
};

export default useNotificationActions;
